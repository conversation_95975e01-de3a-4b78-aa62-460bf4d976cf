# 未激活群组消息处理优化

## 📋 功能概述

修改bot服务，使其对未激活的群组发送的消息不做任何处理，也不进行转发，完全忽略这些消息。

## 🔧 修改内容

### 1. ProcessMessage 函数优化

**文件**: `internal/services/bot.go`

**修改前**:
```go
func (b *BotService) ProcessMessage(message *models.Message) {
    // 记录所有消息
    logger.WithFields(...).Info("Processing message")
    
    groupType := b.getGroupType(message.Chat.ID)
    
    switch groupType {
    // ... 处理各种群组类型
    default:
        logger.WithField("chat_id", message.Chat.ID).Warn("Unknown group type or group is not actived")
    }
}
```

**修改后**:
```go
func (b *BotService) ProcessMessage(message *models.Message) {
    groupType := b.getGroupType(message.Chat.ID)
    
    // 如果群组未激活，直接返回，不做任何处理
    if groupType == models.GroupNotActive {
        logger.WithField("chat_id", message.Chat.ID).Debug("Message from inactive group, ignoring")
        return
    }
    
    // 只有激活的群组才记录处理日志
    logger.WithFields(...).Info("Processing message")
    
    switch groupType {
    // ... 处理各种群组类型
    default:
        logger.WithField("chat_id", message.Chat.ID).Warn("Unknown group type")
    }
}
```

### 2. getGroupType 函数优化

**修改前**:
```go
// 如果数据库中没有找到，默认认为是商户群
newGroup := &models.Group{
    GroupType: models.GroupTypeMerchant,
    IsActive:  false,
}
// ... 创建群组
b.groupTypes.Store(chatID, models.GroupTypeMerchant)
return models.GroupTypeMerchant  // 返回商户群类型
```

**修改后**:
```go
// 如果数据库中没有找到，自动创建一个未激活的群组记录
newGroup := &models.Group{
    GroupType: models.GroupTypeMerchant, // 默认类型，但未激活
    IsActive:  false,                    // 新创建的群组默认未激活
}
// ... 创建群组
b.groupTypes.Store(chatID, models.GroupNotActive)
return models.GroupNotActive  // 返回未激活类型
```

## 🎯 修改效果

### 激活群组
- ✅ 正常处理消息
- ✅ 记录处理日志
- ✅ 执行相应的业务逻辑（订单处理、转发等）

### 未激活群组
- ✅ 完全忽略消息，不做任何处理
- ✅ 不进行消息转发
- ✅ 不执行任何业务逻辑
- ✅ 只记录Debug级别的忽略日志
- ✅ 不会产生任何副作用

### 未知群组（首次接收消息）
- ✅ 自动在数据库中创建群组记录
- ✅ 新创建的群组默认为未激活状态
- ✅ 消息被忽略，直到管理员手动激活群组

## 🔄 缓存机制

系统已有完整的缓存清除机制：

1. **自动缓存清除**: 当群组状态通过API更新时，相关缓存会自动清除
2. **实时生效**: 群组从未激活变为激活后，下一条消息就会被正常处理
3. **线程安全**: 使用 `sync.Map` 确保并发安全

## 📊 日志级别调整

- **激活群组**: `Info` 级别 - "Processing message"
- **未激活群组**: `Debug` 级别 - "Message from inactive group, ignoring"
- **未知群组**: `Warn` 级别 - "Unknown group type"

## 🧪 测试场景

### 场景1: 未激活群组发送消息
1. 群组状态为 `is_active = false`
2. 发送任何消息到该群组
3. **预期结果**: 消息被完全忽略，不产生任何处理

### 场景2: 激活群组后发送消息
1. 通过API将群组设置为 `is_active = true`
2. 发送消息到该群组
3. **预期结果**: 消息被正常处理

### 场景3: 新群组首次发送消息
1. 数据库中不存在该群组记录
2. 发送消息到该群组
3. **预期结果**: 
   - 自动创建未激活的群组记录
   - 消息被忽略
   - 需要管理员手动激活后才能正常处理

## 🔧 管理操作

### 激活群组
```bash
# 通过API激活群组
curl -X PUT http://localhost:8080/api/v1/groups/123 \
  -H "Content-Type: application/json" \
  -d '{"is_active": true}'
```

### 查看群组状态
```bash
# 查看所有群组（包括未激活的）
curl "http://localhost:8080/api/v1/groups/paginated?is_active="

# 只查看未激活的群组
curl "http://localhost:8080/api/v1/groups/paginated?is_active=false"
```

## 📈 性能优化

- **减少无效处理**: 未激活群组的消息不会触发任何业务逻辑
- **降低日志噪音**: 未激活群组只产生Debug级别日志
- **节省资源**: 避免不必要的API调用和数据库查询
- **提高安全性**: 防止未授权群组的消息被处理

## ⚠️ 注意事项

1. **新群组默认未激活**: 所有新检测到的群组都需要管理员手动激活
2. **缓存一致性**: 群组状态更新后会自动清除相关缓存
3. **日志监控**: 可以通过Debug日志监控未激活群组的消息活动
4. **向后兼容**: 不影响现有激活群组的正常功能

---

**版本**: v2.2.0+  
**更新时间**: 2025-07-28  
**影响范围**: 消息处理核心逻辑
